/*
 * OptimizedSpineComponent - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('OptimizedSpineComponent')
export class OptimizedSpineComponent extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.initializeComponent()
    }

    onEnable() {
        this._isInitialized&&this.registerWithMemoryManager()
    }

    onDisable() {
        this.cleanupOnDisable&&this.unregisterFromMemoryManager()
    }

    onDestroy() {
        this.cleanup()
    }

    initializeComponent() {
        var n=r(a().mark((function n(){
            return a().wrap((function(n){
            for(;
        ;
        )switch(n.prev=n.next){
            case 0:return n.prev=0,n.next=3,f.waitForSpine();
        case 3:if(n.sent){
            n.next=6;
        break
    }

}
