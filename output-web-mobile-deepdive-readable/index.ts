/*
 * index - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('index')
export class index extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated from index
 * Reverse engineered by cc-reverse
 *\/

import * as cc from 'cc';
import { SocketManager } from './SocketManager.ts';
import { AuthManager } from './AuthManager.ts';
import { GameManager } from './GameManager.ts';
import { ErrorHandler } from './ErrorHandler.ts';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';

t._RF.push({},"ce384Y9AsVFNYpQD1KSoSRG","index",void 0),t._RF.pop()}}
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
