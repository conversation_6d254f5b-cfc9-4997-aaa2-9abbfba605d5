/*
 * EventManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('EventManager')
export class EventManager extends Component {

    // Extracted methods (may need manual cleanup)
    emit() {
        this.eventTarget.emit(e,t)
    }

    on() {
        this.eventTarget.on(e,t,n)
    }

    off() {
        this.eventTarget.off(e,t,n)
    }

    e({emitEvent:function(e,t){c.instance.emit(e,t)() {
        e({
            emitEvent:function(e,t){
            c.instance.emit(e,t)
    }

    return c.instance() {
        return c.instance
    }

    c.instance.off(e,t,n)() {
        c.instance.off(e,t,n)
    }

    c.instance.on(e,t,n)() {
        c.instance.on(e,t,n)
    }

    return e.instance() {
        return e.instance
    }

}
