/*
 * SoundManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SoundManager')
export class SoundManager extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.createAudioSources(),this.loadSoundPreference()
    }

    start() {
        console.log("[SoundManager] start() - bgmSource:",!!this.bgmSource,"bgmClip:",!!this.bgmClip),this.isBGMEnabled&&this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play(),console.log("[SoundManager] BGM started based on preference")),this.notifyParentWindow(this.isBGMEnabled)
    }

    createAudioSources() {
        this.bgmSource=this.node.addComponent(g),this.bgmSource&&(this.bgmSource.loop=!0,this.bgmSource.playOnAwake=!1,this.bgmSource.clip=this.bgmClip),this.sfxToggleOnSource=this.node.addComponent(g),this.sfxToggleOnSource&&(this.sfxToggleOnSource.loop=!1,this.sfxToggleOnSource.playOnAwake=!1,this.sfxToggleOnSource.clip=this.sfxToggleOnClip),this.sfxToggleOffSource=this.node.addComponent(g),this.sfxToggleOffSource&&(this.sfxToggleOffSource.loop=!1,this.sfxToggleOffSource.playOnAwake=!1,this.sfxToggleOffSource.clip=this.sfxToggleOffClip),console.log("[SoundManager] Audio sources created and clips assigned")
    }

    toggleBGM() {
        return this.isBGMEnabled=!this.isBGMEnabled,this.saveSoundPreference(),this.notifyParentWindow(this.isBGMEnabled),this.isBGMEnabled?(this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()),this.sfxToggleOnSource&&this.sfxToggleOnClip&&(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play())):(this.bgmSource&&this.bgmSource.stop(),this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())),console.log("[SoundManager] BGM "+(this.isBGMEnabled?"enabled":"disabled")),this.isBGMEnabled
    }

    getBGMEnabled() {
        return this.isBGMEnabled
    }

    isBGMPlaying() {
        return(null==(e=this.bgmSource)?void 0:e.playing)||!1
    }

    setBGMEnabled() {
        this.isBGMEnabled!==e&&(this.isBGMEnabled=e,this.saveSoundPreference(),this.notifyParentWindow(e),e&&this.bgmSource&&this.bgmClip?(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()):!e&&this.bgmSource&&this.bgmSource.stop(),console.log("[SoundManager] BGM "+(e?"enabled":"disabled")))
    }

    playToggleSound() {
        e&&this.sfxToggleOnSource&&this.sfxToggleOnClip?(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play()):!e&&this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())
    }

    notifyParentWindow() {
        try{
            if("undefined"!=typeof window&&window.parent&&window.parent!==window){
            var o=e?1:2;
        window.parent.postMessage({
            code:o
    }

    loadSoundPreference() {
        try{
            if("undefined"!=typeof localStorage){
            var e=localStorage.getItem(o.SOUND_PREFERENCE_KEY);
        null!==e?(this.isBGMEnabled="true"===e,console.log("[SoundManager] Loaded sound preference: "+(this.isBGMEnabled?"enabled":"disabled"))):console.log("[SoundManager] No saved sound preference found, using default: enabled")
    }

    saveSoundPreference() {
        try{
            "undefined"!=typeof localStorage&&(localStorage.setItem(o.SOUND_PREFERENCE_KEY,this.isBGMEnabled.toString()),console.log("[SoundManager] Saved sound preference: "+(this.isBGMEnabled?"enabled":"disabled")))
    }

    var f,d,h,p,S,b,m,O,M,T;var x=r.ccclass,y=r.property;e("SoundManager",(f=x("SoundManager"),d=y(a),h=y(a),p=y(a),f(S=u(((T=function(e){function o(){for(var o,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return o=e.call.apply(e,[this].concat(s))||this,i(o,"bgmClip",m,t(o)),i(o,"sfxToggleOnClip",O,t(o)),i(o,"sfxToggleOffClip",M,t(o)),o.bgmSource=null,o.sfxToggleOnSource=null,o.sfxToggleOffSource=null,o.isBGMEnabled=!0,o() {
        var f,d,h,p,S,b,m,O,M,T;
        var x=r.ccclass,y=r.property;
        e("SoundManager",(f=x("SoundManager"),d=y(a),h=y(a),p=y(a),f(S=u(((T=function(e){
            function o(){
            for(var o,n=arguments.length,s=new Array(n),l=0;
        l<n;
        l++)s[l]=arguments[l];
        return o=e.call.apply(e,[this].concat(s))||this,i(o,"bgmClip",m,t(o)),i(o,"sfxToggleOnClip",O,t(o)),i(o,"sfxToggleOffClip",M,t(o)),o.bgmSource=null,o.sfxToggleOnSource=null,o.sfxToggleOffSource=null,o.isBGMEnabled=!0,o
    }

    return o.instance() {
        return o.instance
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

}
