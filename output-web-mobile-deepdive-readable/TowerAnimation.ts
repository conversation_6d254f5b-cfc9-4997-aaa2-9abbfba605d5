/*
 * TowerAnimation - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    ccclass, property } = _decorator;

// Constants for tower animation
const TowerConstants = {
    RESET_Y: 0,
    START_Y: 100,
    PERFORMANCE: {
        ENABLE_DEBUG_LOGGING: true
    }
};

enum TowerPlayer {
    A = 'A',
    P = 'P',
    T = 'T'
}

enum RacePhase {
    PHASE1 = 'phase1',
    PHASE2 = 'phase2',
    PHASE3 = 'phase3'
}

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {
    @property(Node)
    towerNode: Node = null;

    @property({ tooltip: "Tower player identifier (A, P, T)" })
    towerPlayer: TowerPlayer = TowerPlayer.A;

    @property({ tooltip: "Horizontal jiggle amplitude", range: [0, 10, 0.1], slide: true })
    wobbleAmplitude: number = 2.0;

    @property({ tooltip: "Seconds between jiggle flips", range: [0.01, 0.5, 0.01], slide: true })
    wobbleInterval: number = 0.02;

    // Animation state
    private isRacing: boolean = false;
    private currentY: number = TowerConstants.RESET_Y;
    private countdownTween: Tween<Node> = null;
    private currentPhase: RacePhase = RacePhase.PHASE1;

    // Wobble animation state
    private wobbleBaseX: number = 0;
    private wobbleOffset: number = 0;
    private wobbleTimer: number = 0;

    // Missing properties that were found in original code
    private towerA: Node = null;
    private towerP: Node = null;
    private towerT: Node = null;
    private towerAnimations: TowerAnimation[] = [];

    /**
     * Debug logging (only when enabled)
     *\/
    debugLog(...args: any[]) {
        if (TowerConstants.PERFORMANCE.ENABLE_DEBUG_LOGGING) {
            console.log(`[TowerAnimation ${this.towerPlayer}]`, ...args);
        }
    }

    /**
     * Component initialization
     *\/
    onLoad() {
        this.debugLog("onLoad() called - initializing tower animation");

        if (this.towerNode) {
            this.resetPosition();
        }
    }

    /**
     * Reset tower to initial position
     *\/
    resetPosition() {
        if (!this.towerNode) return;

        this.cancelCountdownTween();

        this.currentY = TowerConstants.RESET_Y;
        this.towerNode.setPosition(
            this.towerNode.position.x,
            this.currentY,
            this.towerNode.position.z
        );

        this.currentPhase = RacePhase.PHASE1;
        this.isRacing = false;

        // Reset wobble state
        this.wobbleBaseX = this.towerNode.position.x;
        this.wobbleOffset = 0;
        this.wobbleTimer = 0;

        this.debugLog(`reset to Y=${this.currentY}`);
    }

    /**
     * Cancel any active countdown tween
     *\/
    cancelCountdownTween() {
        if (this.countdownTween) {
            this.countdownTween.stop();
            this.countdownTween = null;
        }
    }

    /**
     * Animate tower to start position before race begins
     * @param duration Animation duration in seconds
     * @param delay Optional delay before starting animation
     *\/
    animateToStartPosition(duration: number = 1.0, delay: number = 0) {
        if (!this.towerNode || this.isRacing) {
            return;
        }

        const tweenDuration = duration + (delay || 0);
        if (tweenDuration === 0) {
            return;
        }

        const targetY = TowerConstants.START_Y;
        const currentPosition = this.towerNode.getPosition();

        // If already at target position, just set the Y value
        if (Math.abs(currentPosition.y - targetY) <= 0.01) {
            this.currentY = targetY;
            return;
        }

        // Cancel any existing tween
        this.cancelCountdownTween();

        // Create smooth tween to start position
        this.countdownTween = tween(this.towerNode)
            .to(tweenDuration, {
                position: new Vec3(currentPosition.x, targetY, currentPosition.z)
            }, {
                easing: 'sineOut',
                onUpdate: () => {
                    this.currentY = this.towerNode.position.y;
                }
            })
            .call(() => {
                this.currentY = targetY;
                this.countdownTween = null;
                this.debugLog(`reached start position Y=${targetY}`);
            })
            .start();
    }

    /**
     * Initialize tower animations for all players
     *\/
    initializeTowerAnimations() {
        const towerNodes = [this.towerA, this.towerP, this.towerT];
        const playerNames = ["A", "P", "T"];

        for (let index = 0; index < towerNodes.length; index++) {
            const towerNode = towerNodes[index];

            if (towerNode) {
                const towerAnimationComponent = towerNode.getComponent(TowerAnimation);

                if (towerAnimationComponent) {
                    towerAnimationComponent.towerPlayer = playerNames[index] as TowerPlayer;
                    this.towerAnimations.push(towerAnimationComponent);
                    this.debugLog(`Initialized tower animation for player ${playerNames[index]}`);
                }
            }
        }
    }

    // Getters
    getCurrentY(): number { return this.currentY; }
    getIsRacing(): boolean { return this.isRacing; }
    getCurrentPhase(): RacePhase { return this.currentPhase; }
}
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
