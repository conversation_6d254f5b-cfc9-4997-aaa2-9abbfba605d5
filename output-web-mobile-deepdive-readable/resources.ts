/*
 * resources - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('resources')
export class resources extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated from resources
 * Reverse engineered by cc-reverse
 *\/

}}
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
