/*
 * internal - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('internal')
export class internal extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated from internal
 * Reverse engineered by cc-reverse
 *\/

import { builtin_pipeline_settings } from './builtin-pipeline-settings.ts';
import { builtin_pipeline_types } from './builtin-pipeline-types.ts';
import { builtin_pipeline } from './builtin-pipeline.ts';

}}
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
