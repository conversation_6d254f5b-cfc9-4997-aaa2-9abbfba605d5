/*
 * builtin_pipeline_types - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('builtin_pipeline_types')
export class builtin_pipeline_types extends Component {

    // Extracted methods (may need manual cleanup)
    void 0===e.enabled&&(e.enabled=!1);void 0===e.radiusScale&&(e.radiusScale=1);void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);void 0===e.blurSharpness&&(e.blurSharpness=3);void 0===e.aoSaturation&&(e.aoSaturation=1);void 0===e.needBlur&&(e.needBlur=!1)() {
        void 0===e.enabled&&(e.enabled=!1);
        void 0===e.radiusScale&&(e.radiusScale=1);
        void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);
        void 0===e.blurSharpness&&(e.blurSharpness=3);
        void 0===e.aoSaturation&&(e.aoSaturation=1);
        void 0===e.needBlur&&(e.needBlur=!1)
    }

    e.msaa?t(e.msaa):e.msaa=r();void 0===e.enableShadingScale&&(e.enableShadingScale=!1);void 0===e.shadingScale&&(e.shadingScale=.5);e.bloom?d(e.bloom):e.bloom=u();e.toneMapping?v(e.toneMapping):e.toneMapping={material:null() {
        e.msaa?t(e.msaa):e.msaa=r();
        void 0===e.enableShadingScale&&(e.enableShadingScale=!1);
        void 0===e.shadingScale&&(e.shadingScale=.5);
        e.bloom?d(e.bloom):e.bloom=u();
        e.toneMapping?v(e.toneMapping):e.toneMapping={
            material:null
    }

    return{enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1() {
        return{
            enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1
    }

    return{msaa:r(),enableShadingScale:!1,shadingScale:.5,bloom:u(),toneMapping:{material:null() {
        return{
            msaa:r(),enableShadingScale:!1,shadingScale:.5,bloom:u(),toneMapping:{
            material:null
    }

}
