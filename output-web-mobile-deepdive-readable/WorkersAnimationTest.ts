/*
 * WorkersAnimationTest - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('WorkersAnimationTest')
export class WorkersAnimationTest extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        this.autoRunTests&&this.workersAnimation&&this.runAllTests()
    }

    runAllTests() {
        var o=this;
        console.log("[WorkersAnimationTest] Starting comprehensive tests..."),this.scheduleTest((function(){
            return o.testSystemValidation()
    }

    scheduleTest() {
        setTimeout(o,t*this.testDelay*1e3)
    }

    testSystemValidation() {
        if(console.log("\n=== Test 1: System Validation ==="),this.workersAnimation){
            var o=this.workersAnimation.getSystemStatus();
        console.log("System Status:",o);
        var t=this.workersAnimation.validateAndFixAnimations();
        console.log("System validation result:",t?"PASSED":"ISSUES FOUND AND FIXED")
    }

    testAnimationClipAnalysis() {
        console.log("\n=== Test 2: Animation Clip Analysis ===");
        var o=this.workersAnimation.getSystemStatus();
        console.log("Animation system status:",o)
    }

    testLoopModeConfiguration() {
        console.log("\n=== Test 3: Loop Mode Configuration ==="),this.workersAnimation.forceLoopMode=!0,console.log("Enabled forceLoopMode"),this.workersAnimation.startIdleMode()
    }

    testEnhancedIdleMode() {
        var o=this;
        console.log("\n=== Test 4: Enhanced Idle Mode ==="),this.workersAnimation.startIdleMode(),setTimeout((function(){
            var t=o.workersAnimation.getSystemStatus();
        console.log("Worker state after idle mode:",t)
    }

    testDifferentWrapModes() {
        var o=this;
        console.log("\n=== Test 5: Different Wrap Modes ==="),this.workersAnimation.stopAllWorkers(),console.log("Testing jiggle mode..."),this.workersAnimation.startJiggleMode(),setTimeout((function(){
            console.log("Testing racing mode..."),o.workersAnimation.startRacingMode()
    }

    testAnimationSpeeds() {
        console.log("\n=== Test 6: Animation Speed Testing ==="),this.workersAnimation.testIdleAnimationSpeeds()
    }

    testFinalValidation() {
        console.log("\n=== Test 7: Final Validation ===");
        var o=this.workersAnimation.getSystemStatus();
        console.log("Final System Status:",o);
        var t=this.workersAnimation.validateAndFixAnimations();
        console.log("Final validation result:",t?"PASSED":"ISSUES FOUND"),console.log("\n=== All Tests Complete ==="),console.log("WorkersAnimation enhanced loop system is ready for use!")
    }

    testIdleMode() {
        this.workersAnimation&&this.workersAnimation.startIdleMode()
    }

    testDebugState() {
        if(this.workersAnimation){
            var o=this.workersAnimation.getSystemStatus();
        console.log("WorkersAnimation debug state:",o)
    }

    testValidateAnimations() {
        if(this.workersAnimation){
            console.log("Animation validation - checking system status...");
        var o=this.workersAnimation.getSystemStatus();
        console.log("System status:",o)
    }

    testForceLoopMode() {
        this.workersAnimation&&(this.workersAnimation.forceLoopMode=!0,this.workersAnimation.startIdleMode(),console.log("Force loop mode enabled and idle mode started"))
    }

    testMonitorLooping() {
        if(this.workersAnimation){
            var o=this.workersAnimation.getSystemStatus();
        console.log("Monitoring animation looping - current status:",o)
    }

    testForceRestartAnimations() {
        this.workersAnimation&&this.workersAnimation.forceRestartAllAnimationsWithLoop()
    }

    quickFixLoopingIssue() {
        this.workersAnimation&&(console.log("[WorkersAnimationTest] Applying quick fix for looping issue..."),this.workersAnimation.quickFixLoopingIssue(),console.log("[WorkersAnimationTest] Quick fix applied using new comprehensive method"))
    }

    testNewLoopSystem() {
        if(this.workersAnimation){
            console.log("[WorkersAnimationTest] Testing new loop management system..."),console.log("1. Testing validation...");
        var o=this.workersAnimation.validateAndFixAnimations();
        console.log("Validation result:",o),console.log("2. Testing force loop..."),this.workersAnimation.forceAllAnimationsToLoop(),console.log("3. Testing debug methods..."),this.workersAnimation.debugWorkerState(),this.workersAnimation.debugAllAnimationClips(),console.log("4. Testing monitoring..."),this.workersAnimation.startLoopMonitoring(),console.log("5. Testing idle mode..."),this.workersAnimation.startIdleMode(),console.log("[WorkersAnimationTest] New loop system test complete")
    }

    testComprehensiveLoopFix() {
        var o=this;
        this.workersAnimation&&(console.log("[WorkersAnimationTest] Testing comprehensive loop fix..."),this.workersAnimation.forceLoopMode=!0,this.workersAnimation.enableEnhancedDebugging=!0,this.workersAnimation.idleAnimationSpeed=1.2,this.workersAnimation.quickFixLoopingIssue(),setTimeout((function(){
            console.log("=== LOOP FIX RESULTS ==="),o.workersAnimation.debugWorkerState(),o.workersAnimation.monitorAnimationLooping()
    }

    return null() {
        return null
    }

    return!1() {
        return!1
    }

    return 3() {
        return 3
    }

}
