/*
 * SpiteSkin - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SpiteSkin')
export class SpiteSkin extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        !this.item&&this.getComponent(a)&&(this.item=this.getComponent(a))
    }

    setSkin() {
        if(i<0||i>this.countSkin())return p("wrong id skin "),void(this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin));
        this.idSkin=i,this.isSprite&&this.item&&(this.spriteSkin[i]?this.item.spriteFrame=this.spriteSkin[i]:this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin)),this.isColor&&this.item&&(this.item.color=this.colorSkin[i])
    }

    countSkin() {
        return this.isSprite&&this.item?this.spriteSkin.length:this.isColor&&this.item?this.colorSkin.length:void 0
    }

    randomSkin() {
        var i=this.getRandomInt(0,this.countSkin()-1);
        this.setSkin(i)
    }

    getRandomInt() {
        return i=Math.ceil(i),t=Math.floor(t),Math.floor(Math.random()*(t-i+1))+i
    }

    return null() {
        return null
    }

    return!1() {
        return!1
    }

    return[]() {
        return[]
    }

    return null() {
        return null
    }

    return!1() {
        return!1
    }

    return[]() {
        return[]
    }

}
