{"group-list": [], "collision-matrix": [], "physics": {"gravity": {"x": 0, "y": -10}, "allowSleep": true, "sleepThreshold": 0.1, "autoSimulation": true, "fixedTimeStep": 0.016666666666666666, "maxSubSteps": 1}, "rendering": {"renderPipeline": "builtin-forward", "shadowType": 1, "shadowDistance": 50, "shadowNormalBias": 0, "shadowBias": 0.0001}, "animation": {"defaultClip": {"duration": 1, "sample": 60, "speed": 1, "wrapMode": 1}}, "assets": {"preloadBundles": [], "remoteBundles": [], "server": ""}, "scripting": {"strictMode": false}, "migrate-history": [], "start-scene": "current"}