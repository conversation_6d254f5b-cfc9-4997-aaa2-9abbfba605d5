/*
 * Auto-generated from RoundInfoPanel
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';
import { LocalizationManager } from './LocalizationManager.ts';
import { WebCommunicationManager } from './WebCommunicationManager.ts';

var T,f,g,R,S,b,D,N,w,U,I,L,E,A,M;s._RF.push({},"50a46L4zitL4r+lA+w+UxH8","RoundInfoPanel",void 0);var P=r.ccclass,y=r.property;e("RoundInfoPanel",(T=P("RoundInfoPanel"),f=y(o),g=y(o),R=y(o),S=y(l),b=y(o),D=y(o),T((U=t((w=function(e){function t(){for(var t,i=arguments.length,s=new Array(i),r=0;r<i;r++)s[r]=arguments[r];return t=e.call.apply(e,[this].concat(s))||this,n(t,"dateLabel",U,a(t)),n(t,"nextRoundLabel",I,a(t)),n(t,"timeToNextRoundLabel",L,a(t)),n(t,"statePanelInfo",E,a(t)),n(t,"seconsLabel",A,a(t)),n(t,"round_info_label",M,a(t)),t._currentRound=0,t._currentStatus=void 0,t._timeRemains=0,t._eventManager=void 0,t._localTimeRemains=0,t._lastServerSync=0,t._serverTime=null,t._serverTimeOffset=0,t._lastServerTimeUpdate=0,t._blinkTween=null,t._lastBlinkTime=-1,t._positionTween=null,t._isAnimating=!1,t.HIDDEN_Y=-500,t.SHOWN_Y=-125,t.ANIMATION_DURATION=.4,t._localizationManager=null,t.firstShow=!0,t.hidePanelEvent=!0,t.hasTriggeredCountdownRaise=!1,t.COUNTDOWN_RAISE_THRESHOLD=3,t}i(t,e);var s=t.prototype;return s.start=function(){this._eventManager=p.instance,this._localizationManager=_.instance,this.privateSetupEventListeners(),this.privateInitializeLocalization(),this.privateInitializeDisplay(),this.privateStartUpdateTimer(),this.seconsLabel.string=this._localizationManager.getSecondsLabel(),this.round_info_label.string=this._localizationManager.getRoundInfoStatic()},s.TestLanguageChange=function(e){this._localizationManager&&this._localizationManager.setLanguage(e)},s.TestRefreshLanguage=function(){this._localizationManager&&this._localizationManager.refreshLanguageFromURL()},s.TestServerTime=function(){this.privateCalculateServerTime();this.privateUpdateDateDisplay()},s.DebugBuildIssue=function(){},s.privateSetupEventListeners=function(){this._eventManager.on(v.GAME_STATUS_CHANGED,this.privateOnGameStatusChanged,this),this._eventManager.on(v.LANGUAGE_CHANGED,this.privateOnLanguageChanged,this),this._eventManager.on(v.RESULT_POPUP_HIDDEN,this.resultHiddenAlready,this),this._eventManager.on(v.GAME_RESET_UI,this.privateOnGameResetUI,this)},s.resultHiddenAlready=function(){this.hidePanelEvent=!0,this.privateAnimateToPosition(0,!0)},s.privateInitializeLocalization=function(){this._localizationManager},s.privateInitializeDisplay=function(){this.statePanelInfo&&(this.statePanelInfo.active=!0);var e=this.node.position.clone();this.node.setPosition(-500,e.y,e.z)},s.privateStartUpdateTimer=function(){this.schedule(this.privateUpdateDateDisplayIfServerTime,1),this.schedule(this.privateUpdateLocalCountdown,1)},s.privateUpdateDateDisplayIfServerTime=function(){this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()},s.privateUpdateLocalCountdown=function(){(this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED)&&this._localTimeRemains>0&&(this._localTimeRemains--,this._localTimeRemains<=0&&this.hidePanel(),this.privateUpdateTimeToNextRound(this._localTimeRemains))},s.hidePanel=function(){var e=this;this.hidePanelEvent&&(this.hidePanelEvent=!1,this.hasTriggeredCountdownRaise=!1,setTimeout((function(){e.privateAnimateToPosition(-500,!1),p.instance.emit(v.STARTUP_POPUP_SHOWN)}),500))},s.privateOnLanguageChanged=function(e){this.privateInitializeLocalization(),this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()},s.privateOnGameStatusChanged=function(e){if(e&&"object"==typeof e){if(void 0!==e.status&&(this._currentStatus=e.status),void 0!==e.round&&(this._currentRound=e.round),void 0!==e.timeRemains){this._timeRemains=e.timeRemains;var t=e.timeRemains;if(this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED){if(this._currentStatus===d.BETTING_OPEN){var i=Math.abs(this._localTimeRemains-t);(this._localTimeRemains<0||i>2)&&(this._localTimeRemains=t,this._lastServerSync=Date.now())}}else{var n=Math.abs(this._localTimeRemains-t);(0===this._localTimeRemains||n>2)&&(this._localTimeRemains=t,this._lastServerSync=Date.now())}t<=0&&(this._localTimeRemains=0)}void 0!==e.server_time&&(this.privateProcessServerTime(e.server_time),this.privateUpdateDateDisplay()),this.checkFirstShow(),this.privateUpdateNextRoundInfo(),this._localTimeRemains>=0&&this.privateUpdateTimeToNextRound(this._localTimeRemains),void 0!==(null==e?void 0:e.timeRemains)&&e.timeRemains<=0&&this.hidePanel()}},s.checkFirstShow=function(){this.statePanelInfo&&this.firstShow&&(this.firstShow=!1,this.privateAnimateToPosition(0,!0))},s.privateAnimateToPosition=function(e,t){var i=this;this._positionTween&&(this._positionTween.stop(),this._positionTween=null),this._isAnimating=!0;var n=this.node.position.clone(),a=new h(e,n.y,n.z);t||(this.hasTriggeredCountdownRaise=!1);var s=m.instance;s&&0==e?s.sendCode4PanelState():console.warn("[RoundInfoPanel] WebCommunicationManager not available when showing panel"),this._positionTween=u(this.node).to(this.ANIMATION_DURATION,{position:a},{easing:"quadInOut"}).call((function(){i._isAnimating=!1,i._positionTween=null,t&&i.privateEmitCountdownRaise()