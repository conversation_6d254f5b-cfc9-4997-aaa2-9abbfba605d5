/*
 * EventManager - Game Component
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('EventManager')
export class EventManager extends Component {

    start() {
        console.log(`[${this.constructor.name}] Component initialized`);
    }

    // Original minified code (needs manual review):
    /*
    var t,n,E,r,o;return{setters:[function(e){t=e.createClass},function(e){n=e.cclegacy,E=e._decorator,r=e.EventTarget},function(e){o=e.singleton}],execute:function(){var a;e({emitEvent:function(e,t){c.instance.emit(e,t)},getEventManager:function(){return c.instance},offEvent:function(e,t,n){c.instance.off(e,t,n)},onEvent:function(e,t,n){c.instance.on(e,t,n)}}),n._RF.push({},"bee05Dk4PNKjacQIm9d7ILk","EventManager",void 0);var _=E.ccclass,c=e("EventManager",_("EventManager")(a=o(a=function(){function e(){this.eventTarget=new r}var n=e.prototype;return n.emit=function(e,t){this.eventTarget.emit(e,t)},n.on=function(e,t,n){this.eventTarget.on(e,t,n)},n.off=function(e,t,n){this.eventTarget.off(e,t,n)},t(e,null,[{key:"instance",get:function(){return e.instance}}]),e}())||a)||a);e("GAME_EVENTS",{GAME_STARTED:"game-started",GAME_STATUS_CHANGED:"game-status-changed",GAME_RESULT:"game-result",GAME_ENDED:"game-ended",SHOW_RESULT_POPUP:"show-result-popup",TOWER_RESULT:"tower-result",TOWER_RESULT_ERROR:"tower-result-error",TOWER_PHASE_CHANGED:"tower-phase-changed",TOWER_INDIVIDUAL_COMPLETE:"tower-individual-complete",TOWER_ALL_RACES_COMPLETE:"tower-all-races-complete",TOWER_DRAMATIC_EVENT:"tower-dramatic-event",TOWER_FIRST_PLACE_FINISHED:"tower-first-place-finished",TOWER_PHASE_1_COMPLETE:"tower-phase-1-complete",TOWER_PHASE_2_COMPLETE:"tower-phase-2-complete",TOWER_RACE_FINISHED:"tower-race-finished",GAME_RESET_UI:"game-reset-ui",ROUND_COUNTDOWN_STARTED:"round-countdown-started",SHOW_INFO_PANEL:"show-info-panel",SCREEN_SHAKE:"screen-shake",AUTH_SUCCESS:"auth-success",AUTH_FAILED:"auth-failed",TOKEN_REQUIRED:"token-required",BET_PLACED:"bet-placed",BET_CANCELLED:"bet-cancelled",BALANCE_UPDATED:"balance-updated",CONNECTION_OPENED:"connection-opened",CONNECTION_CLOSED:"connection-closed",CONNECTION_ERROR:"connection-error",HISTORY_UPDATED:"history-updated",HISTORY_ENTRY_ADDED:"history-entry-added",HISTORY_CLEARED:"history-cleared",UI_READY:"ui-ready",UI_ERROR:"ui-error",STARTUP_POPUP_SHOWN:"startup-popup-shown",STARTUP_POPUP_HIDDEN:"startup-popup-hidden",RESULT_POPUP_SHOWN:"result-popup-shown",RESULT_POPUP_HIDDEN:"result-popup-hidden",LANGUAGE_CHANGED:"language-changed",ERROR_OCCURRED:"error-occurred",RECONNECT_REQUESTED:"reconnect-requested"});n._RF.pop()}}
    */
}