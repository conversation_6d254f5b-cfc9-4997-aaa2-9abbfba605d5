/*
 * Auto-generated from Singleton
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

e({ccsingleton:function(e){var n=null,i=function(i){function s(){return i.apply(this,arguments)||this}t(s,i);var c=s.prototype;return c.onLoad=function(){if(n&&n!==this)return console.warn("[ccsingleton] Multiple instances of "+e.name+" detected. Destroying new instance."),void(this.node&&this.node.isValid&&this.node.destroy());i.prototype.onLoad&&i.prototype.onLoad.call(this),n=this,r.addPersistRootNode(this.node)},c.onDestroy=function(){i.prototype.onDestroy&&i.prototype.onDestroy.call(this),n===this&&(n=null),r.removePersistRootNode(this.node)},o(s,null,[{key:"instance",get:function(){return n}}]),s}(e);return Object.setPrototypeOf(i,e),Object.defineProperty(i,"name",{value:e.name}),i},singleton:function(e){var i=null,r=function(r){function s(){var t;if(i)return console.warn("[singleton] Multiple instances of "+e.name+" detected. Returning existing instance."),i||n(t);for(var o=arguments.length,s=new Array(o),c=0;c<o;c++)s[c]=arguments[c];return t=r.call.apply(r,[this].concat(s))||this,(i=n(t))||n(t)}return t(s,r),o(s,null,[{key:"instance",get:function(){return i||(i=new s),i}}]),s}(e);return Object.setPrototypeOf(r,e),Object.defineProperty(r,"name",{value:e.name}),r}}),i._RF.push({},"938e5CqNSRKgrjEvTm8O/eP","Singleton",void 0),i._RF.pop()}}