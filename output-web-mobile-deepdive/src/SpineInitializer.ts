/*
 * Auto-generated from SpineInitializer
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var p,f;r._RF.push({},"0bb8ayKf1VFlJ+6/NAu1fg7","SpineInitializer",void 0);var d=a.ccclass;i("SpineInitializer",d("SpineInitializer")(((f=function(i){function r(){return i.apply(this,arguments)||this}n(r,i),r.getInstance=function(){return this._instance};var a=r.prototype;return a.onLoad=function(){null===r._instance?(r._instance=this,this.initializeSpine()):this.destroy()},a.onDestroy=function(){r._instance===this&&(r._instance=null)},a.initializeSpine=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!r._isSpineLoaded){i.next=3;break}return o("[SpineInitializer] Spine already loaded"),i.abrupt("return");case 3:if(!r._isLoading){i.next=8;break}return o("[SpineInitializer] Spine loading in progress, waiting..."),i.next=7,r._loadPromise;case 7:return i.abrupt("return");case 8:return r._isLoading=!0,r._loadPromise=this.loadSpineWasm(),i.prev=10,i.next=13,r._loadPromise;case 13:i.sent?(r._isSpineLoaded=!0,o("[SpineInitializer] Spine WASM loaded successfully")):c("[SpineInitializer] Failed to load Spine WASM"),i.next=20;break;case 17:i.prev=17,i.t0=i.catch(10),c("[SpineInitializer] Error loading Spine WASM:",i.t0);case 20:return i.prev=20,r._isLoading=!1,i.finish(20);case 23:case"end":return i.stop()}}),i,this,[[10,17,20,23]])