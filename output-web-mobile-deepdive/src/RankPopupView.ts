/*
 * Auto-generated from RankPopupView
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';
import { WebCommunicationManager } from './WebCommunicationManager.ts';

var g,w,R,T,v,m,P,F,N,b,O,E,I,y,_,A,W,D,V,B,C,L,z,M,U,H,K;a._RF.push({},"ebe84eajHJK9ICTl/+uVu0t","RankPopupView",void 0);var Y=o.ccclass,x=o.property;e("RankPopupView",(g=Y("RankPopupView"),w=x(s),R=x(s),T=x(s),v=x(s),m=x(s),P=x([s]),F=x([s]),N=x([l]),b=x([l]),O=x(c),E=x(c),I=x(c),g((A=t((_=function(e){function t(){for(var t,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,n(t,"bg1Node",A,r(t)),n(t,"bg2Node",W,r(t)),n(t,"aWorkerNode",D,r(t)),n(t,"pWorkerNode",V,r(t)),n(t,"tWorkerNode",B,r(t)),n(t,"rankNodes",C,r(t)),n(t,"bgNodes",L,r(t)),n(t,"faceSprites",z,r(t)),n(t,"defaultFaceSprites",M,r(t)),n(t,"aFaceSprite",U,r(t)),n(t,"pFaceSprite",H,r(t)),n(t,"tFaceSprite",K,r(t)),t.SLIDE_START_OFFSET_X=550,t.SLIDE_DURATION=1.5,t.RANK_POSITIONS=[-150,0,150],t.SLIDE_END_OFFSET_X=-550,t.BOB_AMPLITUDE=8,t.DESIRED_BOB_CYCLES=10,t.RANK_POSITIONSY=[100,55,0],t.slideInTweens=new Map,t.slideOutTweens=new Map,t.rankRevealTweens=new Map,t.blinkingTween=null,t.slideOutTimer=null,t.currentRankOrder=[],t.completedWorkers=0,t.hasEmittedResultPopupHidden=!1,t.thirdWorkerReachedEnd=!1,t}i(t,e);var a=t.prototype;return a.onLoad=function(){this.validateSetup()},a.onDestroy=function(){this.stopAllAnimations()},a.convertPositionsToRankOrder=function(e){var t=[[k.A,e[k.A]],[k.P,e[k.P]],[k.T,e[k.T]]];return t.sort((function(e,t){return e[1]-t[1]