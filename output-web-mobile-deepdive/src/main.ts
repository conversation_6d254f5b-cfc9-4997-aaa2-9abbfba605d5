/*
 * Auto-generated from main
 * Reverse engineered by cc-reverse
 */

import { EventManager } from './EventManager.ts';
import { CardSkin } from './CardSkin.ts';
import { Singleton } from './Singleton.ts';
import { SpiteSkin } from './SpiteSkin.ts';
import { OptimizedSpineComponent } from './OptimizedSpineComponent.ts';
import { LoadingScene } from './LoadingScene.ts';
import { RoundInfoPanel } from './RoundInfoPanel.ts';
import { SoundToggleButton } from './SoundToggleButton.ts';
import { TowerAnimation } from './TowerAnimation.ts';
import { TowerAnimationController } from './TowerAnimationController.ts';
import { WorkersAnimation } from './WorkersAnimation.ts';
import { DynamicPhaseTest } from './DynamicPhaseTest.ts';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';
import { TimingOptimizationTest } from './TimingOptimizationTest.ts';
import { WorkerAnimationDebugger } from './WorkerAnimationDebugger.ts';
import { SpineIntegrationExample } from './SpineIntegrationExample.ts';
import { index } from './index.ts';
import { AuthManager } from './AuthManager.ts';
import { ErrorHandler } from './ErrorHandler.ts';
import { GameManager } from './GameManager.ts';
import { LocalizationManager } from './LocalizationManager.ts';
import { PopupManager } from './PopupManager.ts';
import { SocketManager } from './SocketManager.ts';
import { SoundManager } from './SoundManager.ts';
import { SpineStartupManager } from './SpineStartupManager.ts';
import { WebCommunicationManager } from './WebCommunicationManager.ts';
import { GameEvents } from './GameEvents.ts';
import { WorkersAnimationTest } from './WorkersAnimationTest.ts';
import { SpineInitializer } from './SpineInitializer.ts';
import { SpineMemoryManager } from './SpineMemoryManager.ts';
import { FakeLoadingView } from './FakeLoadingView.ts';
import { HistoryItemView } from './HistoryItemView.ts';
import { RankPopupView } from './RankPopupView.ts';
import { StartPopupView } from './StartPopupView.ts';

}}