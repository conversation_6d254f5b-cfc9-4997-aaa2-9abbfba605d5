/*
 * Auto-generated from HistoryItemView
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { SpiteSkin } from './SpiteSkin.ts';

var c,p,b,y,f,w,S,d,m;o._RF.push({},"b8b44khwl5DQJvI7fTAYD0k","HistoryItemView",void 0);var h=l.ccclass,g=l.property;e("HistoryItemView",(c=h("HistoryItemView"),p=g(s),b=g(s),y=g(a),c((S=t((w=function(e){function t(){for(var t,i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];return t=e.call.apply(e,[this].concat(o))||this,r(t,"roundLabel",S,n(t)),r(t,"resultLabel",d,n(t)),r(t,"iconSpriteSkin",m,n(t)),t}return i(t,e),t.prototype.Setup=function(e){console.log("[HistoryItemView] Setup called with data:",e),this.roundLabel?(this.roundLabel.string=e.round.toString(),console.log("[HistoryItemView] Set round:",e.round)):console.warn("[HistoryItemView] roundLabel not found"),this.resultLabel?(this.resultLabel.string=e.result,console.log("[HistoryItemView] Set result:",e.result,"→",e.result)):console.warn("[HistoryItemView] resultLabel not found"),this.iconSpriteSkin?(this.iconSpriteSkin.setSkin("D"===e.result?0:1),console.log("[HistoryItemView] Set icon skin:","D"===e.result?0:1)):console.warn("[HistoryItemView] iconSpriteSkin not found")},t}(u)).prototype,"roundLabel",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=t(w.prototype,"resultLabel",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=t(w.prototype,"iconSpriteSkin",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=w))||f));o._RF.pop()}}