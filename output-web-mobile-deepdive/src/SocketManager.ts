/*
 * SocketManager - WebSocket Communication Manager
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SocketManager')
export class SocketManager extends Component {
    private static _instance: SocketManager = null;

    private socket: WebSocket = null;
    private isConnected: boolean = false;
    private reconnectAttempts: number = 0;

    static getInstance(): SocketManager {
        return this._instance;
    }

    start() {
        SocketManager._instance = this;
        console.log("[SocketManager] Initialized");
    }

    connect(url: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.isConnected) {
                resolve(true);
                return;
            }

            this.socket = new WebSocket(url);

            this.socket.onopen = () => {
                console.log("[SocketManager] Connected successfully");
                this.isConnected = true;
                this.reconnectAttempts = 0;
                resolve(true);
            };

            this.socket.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.socket.onclose = () => {
                console.log("[SocketManager] Connection closed");
                this.isConnected = false;
            };

            this.socket.onerror = (error) => {
                console.error("[SocketManager] Connection error:", error);
                reject(error);
            };
        });
    }

    send(message: any): boolean {
        if (!this.isConnected || !this.socket) {
            return false;
        }

        try {
            const data = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(data);
            return true;
        } catch (error) {
            console.error("[SocketManager] Failed to send message:", error);
            return false;
        }
    }

    private handleMessage(data: string) {
        try {
            const message = JSON.parse(data);
            console.log("[SocketManager] Received message:", message);
        } catch (error) {
            console.error("[SocketManager] Failed to parse message:", error);
        }
    }

    getConnectionStatus(): boolean { return this.isConnected; }
}