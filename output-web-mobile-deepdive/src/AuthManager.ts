/*
 * AuthManager - Authentication and Token Management
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

interface AuthData {
    token: string;
    playerId: string;
    sessionId: string;
    balance: number;
    isAuthenticated: boolean;
    userProfile?: any;
    bettingLimits?: any;
}

@ccclass('AuthManager')
export class AuthManager extends Component {
    private static _instance: AuthManager = null;

    private _authData: AuthData = {
        token: "",
        playerId: "",
        sessionId: "",
        balance: 0,
        isAuthenticated: false
    };

    static getInstance(): AuthManager {
        return this._instance;
    }

    start() {
        AuthManager._instance = this;
        console.log("[AuthManager] Initialized");
    }

    extractTokenFromURL(): string {
        try {
            const token = new URLSearchParams(window.location.search).get("token");
            if (token) {
                console.log("[AuthManager] Token extracted from URL");
                return decodeURIComponent(token);
            }
            return "";
        } catch (error) {
            console.error("[AuthManager] Failed to extract token from URL:", error);
            return "";
        }
    }

    initializeWithToken(token: string, playerId?: string): { success: boolean; error?: string } {
        if (token && token.trim().length !== 0) {
            return this.initializeAuth(token.trim(), playerId);
        }
        return { success: false, error: "Token parameter is required" };
    }

    validateToken(token: string): { valid: boolean; error?: string } {
        if (!token || token.trim().length === 0) {
            return { valid: false, error: "Token is empty" };
        }

        if (token.length < 50 || token.length > 200) {
            return { valid: false, error: "Token length is invalid" };
        }

        try {
            if (atob(token).length < 10) {
                return { valid: false, error: "Decoded token is too short" };
            }
            return { valid: true };
        } catch (error) {
            return { valid: false, error: "Token is not valid base64" };
        }
    }

    initializeAuth(token: string, playerId?: string): { success: boolean; error?: string } {
        const validation = this.validateToken(token);
        if (validation.valid) {
            this._authData.token = token;
            this._authData.playerId = playerId || "";
            this._authData.isAuthenticated = false;
            return { success: true };
        }
        return { success: false, error: validation.error };
    }

    autoInitialize(): { success: boolean; error?: string } {
        const urlToken = this.extractTokenFromURL();
        if (urlToken) {
            return this.initializeAuth(urlToken);
        }
        return { success: false, error: "No token found" };
    }

    getAuthData(): AuthData {
        return { ...this._authData };
    }

    isAuthenticated(): boolean {
        return this._authData.isAuthenticated && this._authData.token.length > 0;
    }
}