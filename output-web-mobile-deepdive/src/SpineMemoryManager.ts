/*
 * Auto-generated from SpineMemoryManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var c,l,u,h,d,y,M,f,g,S,v,C;r._RF.push({},"0059eKxGm9PRabx/z2fw7Rt","SpineMemoryManager",void 0);var _=a.ccclass,z=a.property;e("SpineMemoryManager",(c=_("SpineMemoryManager"),l=z({displayName:"Max Concurrent Animations",tooltip:"Maximum number of Spine animations that can run simultaneously"}),u=z({displayName:"Memory Threshold (MB)",tooltip:"Memory threshold in MB before optimization kicks in"}),h=z({displayName:"Enable Auto Cleanup",tooltip:"Automatically cleanup unused Spine resources"}),d=z({displayName:"Cleanup Interval (seconds)",tooltip:"Interval for automatic cleanup in seconds"}),c(((C=function(e){function n(){for(var n,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return(n=e.call.apply(e,[this].concat(r))||this)._activeSpineComponents=new Set,n._memoryThreshold=52428800,n._maxConcurrentAnimations=5,n._isMemoryOptimized=!1,t(n,"maxConcurrentAnimations",f,o(n)),t(n,"memoryThresholdMB",g,o(n)),t(n,"enableAutoCleanup",S,o(n)),t(n,"cleanupInterval",v,o(n)),n}i(n,e),n.getInstance=function(){return this._instance};var r=n.prototype;return r.onLoad=function(){null===n._instance?(n._instance=this,this.initializeMemoryManager()):this.destroy()},r.onDestroy=function(){n._instance===this&&(this.cleanup(),n._instance=null)},r.initializeMemoryManager=function(){this._memoryThreshold=1024*this.memoryThresholdMB*1024,this._maxConcurrentAnimations=this.maxConcurrentAnimations,this.enableAutoCleanup&&this.schedule(this.performCleanup,this.cleanupInterval),this.schedule(this.monitorMemoryUsage,5),s("[SpineMemoryManager] Initialized with settings:",{maxConcurrentAnimations:this._maxConcurrentAnimations,memoryThreshold:this.memoryThresholdMB+"MB",autoCleanup:this.enableAutoCleanup})},r.registerSpineComponent=function(e){e?(this._activeSpineComponents.add(e),this._activeSpineComponents.size>this._maxConcurrentAnimations&&this.optimizeMemoryUsage(),s("[SpineMemoryManager] Registered spine component. Active count: "+this._activeSpineComponents.size)):m("[SpineMemoryManager] Cannot register null spine component")},r.unregisterSpineComponent=function(e){this._activeSpineComponents.has(e)&&(this._activeSpineComponents.delete(e),s("[SpineMemoryManager] Unregistered spine component. Active count: "+this._activeSpineComponents.size))},r.optimizeMemoryUsage=function(){var e=this;if(!this._isMemoryOptimized){this._isMemoryOptimized=!0,s("[SpineMemoryManager] Starting memory optimization...");var n=0,i=Array.from(this._activeSpineComponents);i.sort((function(e,n){var i=e.node.active&&e.enabled,t=n.node.active&&n.enabled;return i&&!t?-1:!i&&t?1:0