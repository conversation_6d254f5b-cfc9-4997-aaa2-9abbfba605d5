/*
 * Auto-generated from WorkersAnimationTest
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { WorkersAnimation } from './WorkersAnimation.ts';

var c,m,u,A,d,g,h,p,k;s._RF.push({},"e987c1FrvBIuIVp8XPYaG2C","WorkersAnimationTest",void 0);var f=r.ccclass,w=r.property;o("WorkersAnimationTest",(c=f("WorkersAnimationTest"),m=w({type:l,tooltip:"Reference to WorkersAnimation component"}),u=w({tooltip:"Auto-run tests on start"}),A=w({tooltip:"Test delay between operations (seconds)"}),c((h=t((g=function(o){function t(){for(var t,e=arguments.length,s=new Array(e),r=0;r<e;r++)s[r]=arguments[r];return t=o.call.apply(o,[this].concat(s))||this,n(t,"workersAnimation",h,i(t)),n(t,"autoRunTests",p,i(t)),n(t,"testDelay",k,i(t)),t}e(t,o);var s=t.prototype;return s.start=function(){this.autoRunTests&&this.workersAnimation&&this.runAllTests()},s.runAllTests=function(){var o=this;console.log("[WorkersAnimationTest] Starting comprehensive tests..."),this.scheduleTest((function(){return o.testSystemValidation()}),1),this.scheduleTest((function(){return o.testAnimationClipAnalysis()}),2),this.scheduleTest((function(){return o.testLoopModeConfiguration()}),3),this.scheduleTest((function(){return o.testEnhancedIdleMode()}),4),this.scheduleTest((function(){return o.testDifferentWrapModes()}),5),this.scheduleTest((function(){return o.testAnimationSpeeds()}),6),this.scheduleTest((function(){return o.testFinalValidation()}),7)},s.scheduleTest=function(o,t){setTimeout(o,t*this.testDelay*1e3)},s.testSystemValidation=function(){if(console.log("\n=== Test 1: System Validation ==="),this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("System Status:",o);var t=this.workersAnimation.validateAndFixAnimations();console.log("System validation result:",t?"PASSED":"ISSUES FOUND AND FIXED")}else console.error("WorkersAnimation component not assigned!")},s.testAnimationClipAnalysis=function(){console.log("\n=== Test 2: Animation Clip Analysis ===");var o=this.workersAnimation.getSystemStatus();console.log("Animation system status:",o)},s.testLoopModeConfiguration=function(){console.log("\n=== Test 3: Loop Mode Configuration ==="),this.workersAnimation.forceLoopMode=!0,console.log("Enabled forceLoopMode"),this.workersAnimation.startIdleMode()},s.testEnhancedIdleMode=function(){var o=this;console.log("\n=== Test 4: Enhanced Idle Mode ==="),this.workersAnimation.startIdleMode(),setTimeout((function(){var t=o.workersAnimation.getSystemStatus();console.log("Worker state after idle mode:",t)}),1e3)},s.testDifferentWrapModes=function(){var o=this;console.log("\n=== Test 5: Different Wrap Modes ==="),this.workersAnimation.stopAllWorkers(),console.log("Testing jiggle mode..."),this.workersAnimation.startJiggleMode(),setTimeout((function(){console.log("Testing racing mode..."),o.workersAnimation.startRacingMode()}),2e3)},s.testAnimationSpeeds=function(){console.log("\n=== Test 6: Animation Speed Testing ==="),this.workersAnimation.testIdleAnimationSpeeds()},s.testFinalValidation=function(){console.log("\n=== Test 7: Final Validation ===");var o=this.workersAnimation.getSystemStatus();console.log("Final System Status:",o);var t=this.workersAnimation.validateAndFixAnimations();console.log("Final validation result:",t?"PASSED":"ISSUES FOUND"),console.log("\n=== All Tests Complete ==="),console.log("WorkersAnimation enhanced loop system is ready for use!")},s.testIdleMode=function(){this.workersAnimation&&this.workersAnimation.startIdleMode()},s.testDebugState=function(){if(this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("WorkersAnimation debug state:",o)}},s.testValidateAnimations=function(){if(this.workersAnimation){console.log("Animation validation - checking system status...");var o=this.workersAnimation.getSystemStatus();console.log("System status:",o)}},s.testForceLoopMode=function(){this.workersAnimation&&(this.workersAnimation.forceLoopMode=!0,this.workersAnimation.startIdleMode(),console.log("Force loop mode enabled and idle mode started"))},s.testMonitorLooping=function(){if(this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("Monitoring animation looping - current status:",o)}},s.testForceRestartAnimations=function(){this.workersAnimation&&this.workersAnimation.forceRestartAllAnimationsWithLoop()},s.quickFixLoopingIssue=function(){this.workersAnimation&&(console.log("[WorkersAnimationTest] Applying quick fix for looping issue..."),this.workersAnimation.quickFixLoopingIssue(),console.log("[WorkersAnimationTest] Quick fix applied using new comprehensive method"))},s.testNewLoopSystem=function(){if(this.workersAnimation){console.log("[WorkersAnimationTest] Testing new loop management system..."),console.log("1. Testing validation...");var o=this.workersAnimation.validateAndFixAnimations();console.log("Validation result:",o),console.log("2. Testing force loop..."),this.workersAnimation.forceAllAnimationsToLoop(),console.log("3. Testing debug methods..."),this.workersAnimation.debugWorkerState(),this.workersAnimation.debugAllAnimationClips(),console.log("4. Testing monitoring..."),this.workersAnimation.startLoopMonitoring(),console.log("5. Testing idle mode..."),this.workersAnimation.startIdleMode(),console.log("[WorkersAnimationTest] New loop system test complete")}},s.testComprehensiveLoopFix=function(){var o=this;this.workersAnimation&&(console.log("[WorkersAnimationTest] Testing comprehensive loop fix..."),this.workersAnimation.forceLoopMode=!0,this.workersAnimation.enableEnhancedDebugging=!0,this.workersAnimation.idleAnimationSpeed=1.2,this.workersAnimation.quickFixLoopingIssue(),setTimeout((function(){console.log("=== LOOP FIX RESULTS ==="),o.workersAnimation.debugWorkerState(),o.workersAnimation.monitorAnimationLooping()}),3e3),console.log("[WorkersAnimationTest] Comprehensive loop fix test initiated"))},t}(a)).prototype,"workersAnimation",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=t(g.prototype,"autoRunTests",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),k=t(g.prototype,"testDelay",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),d=g))||d));s._RF.pop()}}