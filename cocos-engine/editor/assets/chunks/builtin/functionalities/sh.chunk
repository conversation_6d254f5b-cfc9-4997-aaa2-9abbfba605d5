#pragma define-meta CC_USE_LIGHT_PROBE default(false)

#if CC_USE_LIGHT_PROBE
vec3 SHEvaluate(vec3 normal)
{
    vec3 result;

#if USE_INSTANCING
    // calculate linear and const terms
    vec4 normal4 = vec4(normal, 1.0);
    result.r = dot(v_sh_linear_const_r, normal4);
    result.g = dot(v_sh_linear_const_g, normal4);
    result.b = dot(v_sh_linear_const_b, normal4);

#else
    // calculate linear and const terms
    vec4 normal4 = vec4(normal, 1.0);
    result.r = dot(cc_sh_linear_const_r, normal4);
    result.g = dot(cc_sh_linear_const_g, normal4);
    result.b = dot(cc_sh_linear_const_b, normal4);

    // calculate quadratic terms
    vec4 n14 = normal.xyzz * normal.yzzx;
    float n5 = normal.x * normal.x - normal.y * normal.y;

    result.r += dot(cc_sh_quadratic_r, n14);
    result.g += dot(cc_sh_quadratic_g, n14);
    result.b += dot(cc_sh_quadratic_b, n14);
    result += (cc_sh_quadratic_a.rgb * n5);
#endif

  #if CC_USE_HDR
    // convert from standard camera exposure parameters to current exposure value
    // baked in LDR scene still regarded as exposured with standard camera parameters
    result *= cc_exposure.w * cc_exposure.x;
  #endif

  return result;
}
#endif
